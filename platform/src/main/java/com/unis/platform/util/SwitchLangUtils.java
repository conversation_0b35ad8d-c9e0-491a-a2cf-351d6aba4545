package com.unis.platform.util;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.os.LocaleList;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.DisplayMetrics;

import com.linc.platform.core.LocalPersistence;
import com.linc.platform.utils.ResUtil;

import java.util.Locale;

public class SwitchLangUtils {

    public static final void setLanguage(@Nullable Locale locale) {
        if (null == locale) {
            LocalPersistence.removeCurrentLang(ResUtil.getContext());
        } else {
            LocalPersistence.saveCurrentLang(ResUtil.getContext(), locale.getLanguage());
        }
        restartApp();
    }

    public static final void restartApp() {
        ActivityManager manager = (ActivityManager) ResUtil.getContext().getSystemService(Context.ACTIVITY_SERVICE);
        if (null == manager) {
            return;
        }
        try {
            ActivityManager.RunningTaskInfo info = (ActivityManager.RunningTaskInfo) manager.getRunningTasks(1).get(0);
            Intent intent = new Intent();
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            intent.setClass(ResUtil.getContext(), Class.forName(info.baseActivity.getClassName()));
            ResUtil.getContext().startActivity(intent);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
    }

    public static final void setDefaultLanguage(@Nullable Locale locale) {

    }

    public static final Context wrapContext(@Nullable Context context) {
        Resources resources = context != null ? context.getResources() : null;
        Configuration configuration = resources != null ? resources.getConfiguration() : null;
        String currentLang = getSettingLang();
        Locale locale = createOptimalLocale(currentLang);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            if (configuration != null) {
                configuration.setLocale(locale);
            }
            LocaleList localeList = new LocaleList(new Locale[]{locale});
            LocaleList.setDefault(localeList);
            if (configuration != null) {
                configuration.setLocales(localeList);
            }
        } else if (configuration != null) {
            configuration.setLocale(locale);
        }
        return context != null ? context.createConfigurationContext(configuration) : null;
    }

    public static final void applyContextAndApplication(@Nullable Context context) {
        applyChange(context);
        applyChange(context != null ? context.getApplicationContext() : null);
    }

    private static final void applyChange(Context context) {
        Resources res = context != null ? context.getResources() : null;
        DisplayMetrics dm = res != null ? res.getDisplayMetrics() : null;
        Configuration conf = res != null ? res.getConfiguration() : null;
        String lang = getSettingLang();
        Locale defaultLocale = Locale.getDefault();
        Locale locale = new Locale(lang, defaultLocale.getCountry());
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            if (conf != null) {
                conf.setLocale(locale);
            }

            LocaleList localeList = new LocaleList(new Locale[]{locale});
            LocaleList.setDefault(localeList);
            if (conf != null) {
                conf.setLocales(localeList);
            }
        } else if (conf != null) {
            conf.setLocale(locale);
        }

        if (res != null) {
            res.updateConfiguration(conf, dm);
        }

    }

    public static final String getSettingLang() {
        if (isSettedLang()) {
            return LocalPersistence.getCurrentLang(ResUtil.getContext());
        } else {
            return getSystemLanguage();
        }
    }

    public static String getSystemLanguage() {
        Locale locale;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            locale = Resources.getSystem().getConfiguration().getLocales().get(0);
        } else {
            locale = Resources.getSystem().getConfiguration().locale;
        }

        return locale.getLanguage();
    }

    public static final boolean isSettedLang() {
        String currentLang = LocalPersistence.getCurrentLang(ResUtil.getContext());
        return !TextUtils.isEmpty(currentLang);
    }

    public static final boolean isLanguageEn(){
        String lang = getSettingLang();
        return lang.equalsIgnoreCase(Locale.ENGLISH.getLanguage());
    }

    public static Locale getLanguage() {
        String lang = getSettingLang();
        if (lang.equalsIgnoreCase(Locale.CHINESE.getLanguage())) {
            return Locale.CHINESE;
        } else if (lang.equalsIgnoreCase(new Locale("es").getLanguage())) {
            return new Locale("es");
        } else if (lang.equalsIgnoreCase(new Locale("ja").getLanguage())) {
            return new Locale("ja");
        } else {
            return Locale.ENGLISH;
        }
    }

}
